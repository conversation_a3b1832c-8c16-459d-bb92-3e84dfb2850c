-- =====================================================
-- TINDAHAN STORE - CONSOLIDATED DATABASE SCHEMA
-- =====================================================
-- Professional database setup for Tindahan store management system
-- This file contains all necessary tables, views, functions, triggers, and sample data
-- Can be directly pasted into Supabase SQL Editor for complete setup
--
-- Features:
-- ✅ Product Management with inventory tracking
-- ✅ Customer Profile Management with Cloudinary support
-- ✅ Comprehensive indexing for optimal performance
-- ✅ Automatic timestamp management
-- ✅ Sample data for immediate testing
-- =====================================================

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- PRODUCTS TABLE
-- =====================================================
-- Manages store inventory with stock tracking
CREATE TABLE IF NOT EXISTS products (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    image_url TEXT,
    net_weight VARCHAR(100) NOT NULL,
    price DECIMAL(10,2) NOT NULL CHECK (price >= 0),
    stock_quantity INTEGER NOT NULL DEFAULT 0 CHECK (stock_quantity >= 0),
    category VARCHAR(100) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- CUSTOMERS TABLE
-- =====================================================
-- Manages customer profiles with Cloudinary image support
CREATE TABLE IF NOT EXISTS customers (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    customer_name VARCHAR(255) NOT NULL,
    customer_family_name VARCHAR(255) NOT NULL,
    profile_picture_url TEXT,
    profile_picture_public_id TEXT, -- Cloudinary public ID for image management
    phone_number VARCHAR(20),
    address TEXT,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(customer_name, customer_family_name)
);





-- =====================================================
-- PERFORMANCE INDEXES
-- =====================================================
-- Optimized indexes for fast queries and better performance

-- Product indexes
CREATE INDEX IF NOT EXISTS idx_products_category ON products(category);
CREATE INDEX IF NOT EXISTS idx_products_name ON products(name);
CREATE INDEX IF NOT EXISTS idx_products_price ON products(price);
CREATE INDEX IF NOT EXISTS idx_products_stock ON products(stock_quantity);

-- Customer indexes
CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(customer_name, customer_family_name);
CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers(phone_number);



-- =====================================================
-- FUNCTIONS AND TRIGGERS
-- =====================================================
-- Automatic timestamp management and business logic

-- Function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';



-- Create triggers to automatically update updated_at timestamps
DROP TRIGGER IF EXISTS update_products_updated_at ON products;
CREATE TRIGGER update_products_updated_at
    BEFORE UPDATE ON products
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_customers_updated_at ON customers;
CREATE TRIGGER update_customers_updated_at
    BEFORE UPDATE ON customers
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();



-- =====================================================
-- SAMPLE DATA FOR TESTING
-- =====================================================
-- Comprehensive sample data to test all functionality immediately
--
-- SAFE INSERTION APPROACH:
-- Uses "INSERT ... SELECT ... WHERE NOT EXISTS" pattern to prevent
-- duplicate key violations when re-running the schema.
-- This makes the schema safe to run multiple times without errors.
-- =====================================================

-- Insert sample products with diverse categories (with safe duplicate handling)
INSERT INTO products (name, net_weight, price, stock_quantity, category)
SELECT 'Lucky Me Pancit Canton', '60g', 15.00, 50, 'Instant Foods'
WHERE NOT EXISTS (SELECT 1 FROM products WHERE name = 'Lucky Me Pancit Canton');

INSERT INTO products (name, net_weight, price, stock_quantity, category)
SELECT 'Coca-Cola', '330ml', 25.00, 30, 'Beverages'
WHERE NOT EXISTS (SELECT 1 FROM products WHERE name = 'Coca-Cola');

INSERT INTO products (name, net_weight, price, stock_quantity, category)
SELECT 'Corned Beef', '150g', 45.00, 20, 'Canned Goods'
WHERE NOT EXISTS (SELECT 1 FROM products WHERE name = 'Corned Beef');

INSERT INTO products (name, net_weight, price, stock_quantity, category)
SELECT 'Shampoo Sachet', '12ml', 8.00, 100, 'Personal Care'
WHERE NOT EXISTS (SELECT 1 FROM products WHERE name = 'Shampoo Sachet');

INSERT INTO products (name, net_weight, price, stock_quantity, category)
SELECT 'Instant Coffee', '25g', 12.00, 75, 'Beverages'
WHERE NOT EXISTS (SELECT 1 FROM products WHERE name = 'Instant Coffee');

INSERT INTO products (name, net_weight, price, stock_quantity, category)
SELECT 'Rice', '1kg', 55.00, 25, 'Rice & Grains'
WHERE NOT EXISTS (SELECT 1 FROM products WHERE name = 'Rice');

INSERT INTO products (name, net_weight, price, stock_quantity, category)
SELECT 'Soy Sauce', '200ml', 18.00, 40, 'Condiments'
WHERE NOT EXISTS (SELECT 1 FROM products WHERE name = 'Soy Sauce');

INSERT INTO products (name, net_weight, price, stock_quantity, category)
SELECT 'Detergent Powder', '35g', 6.00, 80, 'Household Items'
WHERE NOT EXISTS (SELECT 1 FROM products WHERE name = 'Detergent Powder');

INSERT INTO products (name, net_weight, price, stock_quantity, category)
SELECT 'Bread Loaf', '450g', 35.00, 15, 'Bakery'
WHERE NOT EXISTS (SELECT 1 FROM products WHERE name = 'Bread Loaf');

INSERT INTO products (name, net_weight, price, stock_quantity, category)
SELECT 'Cooking Oil', '1L', 85.00, 12, 'Cooking Essentials'
WHERE NOT EXISTS (SELECT 1 FROM products WHERE name = 'Cooking Oil');

-- Insert sample customer profiles (with safe duplicate handling)
INSERT INTO customers (customer_name, customer_family_name, phone_number, address, notes)
SELECT 'Juan', 'Dela Cruz', '09123456789', 'Barangay San Jose, Quezon City', 'Regular customer - prefers instant foods'
WHERE NOT EXISTS (SELECT 1 FROM customers WHERE customer_name = 'Juan' AND customer_family_name = 'Dela Cruz');

INSERT INTO customers (customer_name, customer_family_name, phone_number, address, notes)
SELECT 'Maria', 'Santos', '09234567890', 'Barangay Maligaya, Manila', 'Frequent buyer of beverages'
WHERE NOT EXISTS (SELECT 1 FROM customers WHERE customer_name = 'Maria' AND customer_family_name = 'Santos');

INSERT INTO customers (customer_name, customer_family_name, phone_number, address, notes)
SELECT 'Pedro', 'Garcia', '09345678901', 'Barangay Bagong Silang, Caloocan', 'Prefers canned goods'
WHERE NOT EXISTS (SELECT 1 FROM customers WHERE customer_name = 'Pedro' AND customer_family_name = 'Garcia');

INSERT INTO customers (customer_name, customer_family_name, phone_number, address, notes)
SELECT 'Ana', 'Reyes', '09456789012', 'Barangay Tatalon, Quezon City', 'Coffee lover - regular customer'
WHERE NOT EXISTS (SELECT 1 FROM customers WHERE customer_name = 'Ana' AND customer_family_name = 'Reyes');

INSERT INTO customers (customer_name, customer_family_name, phone_number, address, notes)
SELECT 'Jose', 'Cruz', '09567890123', 'Barangay Payatas, Quezon City', 'Bulk rice buyer'
WHERE NOT EXISTS (SELECT 1 FROM customers WHERE customer_name = 'Jose' AND customer_family_name = 'Cruz');

INSERT INTO customers (customer_name, customer_family_name, phone_number, address, notes)
SELECT 'Rosa', 'Mendoza', '***********', 'Barangay Commonwealth, Quezon City', 'Family with young children'
WHERE NOT EXISTS (SELECT 1 FROM customers WHERE customer_name = 'Rosa' AND customer_family_name = 'Mendoza');

INSERT INTO customers (customer_name, customer_family_name, phone_number, address, notes)
SELECT 'Carlos', 'Villanueva', '***********', 'Barangay Fairview, Quezon City', 'Small business owner'
WHERE NOT EXISTS (SELECT 1 FROM customers WHERE customer_name = 'Carlos' AND customer_family_name = 'Villanueva');




-- =====================================================
-- VERIFICATION AND SETUP CONFIRMATION
-- =====================================================
-- Queries to verify successful setup

-- Verify tables were created
SELECT 'Database setup completed successfully!' as status;

SELECT 'Tables created:' as info;
SELECT table_name,
       (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = t.table_name AND table_schema = 'public') as column_count
FROM information_schema.tables t
WHERE table_schema = 'public'
AND table_name IN ('products', 'customers')
ORDER BY table_name;



-- Verify indexes were created
SELECT 'Indexes created:' as info;
SELECT COUNT(*) as index_count
FROM pg_indexes
WHERE schemaname = 'public'
AND indexname LIKE 'idx_%';

-- Sample data verification
SELECT 'Sample data summary:' as info;
SELECT 'Products: ' || COUNT(*) as count FROM products
UNION ALL
SELECT 'Customers: ' || COUNT(*) as count FROM customers;



-- =====================================================
-- SETUP COMPLETE
-- =====================================================
-- Your Tindahan store database is now ready!
--
-- Key Features Available:
-- ✅ Product inventory management
-- ✅ Customer profile management with Cloudinary support
-- ✅ Performance optimized with proper indexing
-- ✅ Automatic timestamp management
-- ✅ Sample data for immediate testing
--
-- Next Steps:
-- 1. Use the API endpoints to interact with the database
-- 2. Add products and manage inventory
-- 3. Manage customer profiles
-- 4. Monitor performance and add additional indexes if needed
-- =====================================================
