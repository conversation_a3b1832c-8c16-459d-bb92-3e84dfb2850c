-- =====================================================
-- TINDAHAN STORE - COMPLETE DATABASE SCHEMA
-- =====================================================
-- Professional database setup for Tindahan store management system
-- This file contains all necessary tables, views, functions, triggers, and sample data
-- Can be directly pasted into Supabase SQL Editor for complete setup
--
-- Features:
-- ✅ Product Management with inventory tracking
-- ✅ Customer Profile Management with Cloudinary support
-- ✅ Debt Management System with payment tracking
-- ✅ Family member payment responsibility tracking
-- ✅ Real-time balance calculations
-- ✅ Row Level Security (RLS) policies
-- ✅ Comprehensive indexing for optimal performance
-- ✅ Automatic timestamp management
-- ✅ Sample data for immediate testing
-- =====================================================

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- PRODUCTS TABLE
-- =====================================================
-- Manages store inventory with stock tracking
CREATE TABLE IF NOT EXISTS products (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    image_url TEXT,
    net_weight VARCHAR(100) NOT NULL,
    price DECIMAL(10,2) NOT NULL CHECK (price >= 0),
    stock_quantity INTEGER NOT NULL DEFAULT 0 CHECK (stock_quantity >= 0),
    category VARCHAR(100) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- CUSTOMERS TABLE
-- =====================================================
-- Manages customer profiles with Cloudinary image support
CREATE TABLE IF NOT EXISTS customers (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    customer_name VARCHAR(255) NOT NULL,
    customer_family_name VARCHAR(255) NOT NULL,
    profile_picture_url TEXT,
    profile_picture_public_id TEXT, -- Cloudinary public ID for image management
    phone_number VARCHAR(20),
    address TEXT,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(customer_name, customer_family_name)
);

-- =====================================================
-- CUSTOMER DEBTS TABLE
-- =====================================================
-- Manages customer debt records with product details
CREATE TABLE IF NOT EXISTS customer_debts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    customer_name VARCHAR(255) NOT NULL,
    customer_family_name VARCHAR(255) NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    product_price DECIMAL(10,2) NOT NULL CHECK (product_price > 0),
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    total_amount DECIMAL(10,2) GENERATED ALWAYS AS (product_price * quantity) STORED,
    debt_date DATE NOT NULL DEFAULT CURRENT_DATE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- CUSTOMER PAYMENTS TABLE
-- =====================================================
-- Manages payment records with family member tracking
CREATE TABLE IF NOT EXISTS customer_payments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    customer_name VARCHAR(255) NOT NULL,
    customer_family_name VARCHAR(255) NOT NULL,
    payment_amount DECIMAL(10,2) NOT NULL CHECK (payment_amount > 0),
    payment_date DATE NOT NULL DEFAULT CURRENT_DATE,
    payment_method VARCHAR(50) DEFAULT 'Cash',
    responsible_family_member VARCHAR(255), -- Optional: Name of family member who made payment
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);


-- =====================================================
-- CUSTOMER BALANCE VIEW
-- =====================================================
-- Real-time view for customer debt balances
CREATE OR REPLACE VIEW customer_balances AS
SELECT
    customer_name,
    customer_family_name,
    COALESCE(total_debt, 0) as total_debt,
    COALESCE(total_payments, 0) as total_payments,
    COALESCE(total_debt, 0) - COALESCE(total_payments, 0) as remaining_balance,
    last_debt_date,
    last_payment_date,
    debt_count,
    payment_count
FROM (
    SELECT
        customer_name,
        customer_family_name,
        SUM(total_amount) as total_debt,
        MAX(debt_date) as last_debt_date,
        COUNT(*) as debt_count
    FROM customer_debts
    GROUP BY customer_name, customer_family_name
) debts
FULL OUTER JOIN (
    SELECT
        customer_name,
        customer_family_name,
        SUM(payment_amount) as total_payments,
        MAX(payment_date) as last_payment_date,
        COUNT(*) as payment_count
    FROM customer_payments
    GROUP BY customer_name, customer_family_name
) payments USING (customer_name, customer_family_name);

-- =====================================================
-- PERFORMANCE INDEXES
-- =====================================================
-- Optimized indexes for fast queries and better performance

-- Product indexes
CREATE INDEX IF NOT EXISTS idx_products_category ON products(category);
CREATE INDEX IF NOT EXISTS idx_products_name ON products(name);
CREATE INDEX IF NOT EXISTS idx_products_price ON products(price);
CREATE INDEX IF NOT EXISTS idx_products_stock ON products(stock_quantity);

-- Customer indexes
CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(customer_name, customer_family_name);
CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers(phone_number);

-- Customer debt indexes
CREATE INDEX IF NOT EXISTS idx_customer_debts_customer ON customer_debts(customer_name, customer_family_name);
CREATE INDEX IF NOT EXISTS idx_customer_debts_product ON customer_debts(product_name);
CREATE INDEX IF NOT EXISTS idx_customer_debts_date ON customer_debts(debt_date);
CREATE INDEX IF NOT EXISTS idx_customer_debts_amount ON customer_debts(total_amount);

-- Customer payment indexes
CREATE INDEX IF NOT EXISTS idx_customer_payments_customer ON customer_payments(customer_name, customer_family_name);
CREATE INDEX IF NOT EXISTS idx_customer_payments_date ON customer_payments(payment_date);
CREATE INDEX IF NOT EXISTS idx_customer_payments_amount ON customer_payments(payment_amount);
CREATE INDEX IF NOT EXISTS idx_customer_payments_method ON customer_payments(payment_method);


-- =====================================================
-- FUNCTIONS AND TRIGGERS
-- =====================================================
-- Automatic timestamp management and business logic

-- Function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to automatically update updated_at timestamps
DROP TRIGGER IF EXISTS update_products_updated_at ON products;
CREATE TRIGGER update_products_updated_at
    BEFORE UPDATE ON products
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_customers_updated_at ON customers;
CREATE TRIGGER update_customers_updated_at
    BEFORE UPDATE ON customers
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_customer_debts_updated_at ON customer_debts;
CREATE TRIGGER update_customer_debts_updated_at
    BEFORE UPDATE ON customer_debts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_customer_payments_updated_at ON customer_payments;
CREATE TRIGGER update_customer_payments_updated_at
    BEFORE UPDATE ON customer_payments
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- SECURITY POLICIES (RLS)
-- =====================================================
-- Enable Row Level Security for data protection

-- Enable RLS on all tables
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_debts ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_payments ENABLE ROW LEVEL SECURITY;

-- Create policies for authenticated users (allows all operations for authenticated users)
CREATE POLICY "Enable all operations for authenticated users" ON products
FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Enable all operations for authenticated users" ON customers
FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Enable all operations for authenticated users" ON customer_debts
FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Enable all operations for authenticated users" ON customer_payments
FOR ALL USING (auth.role() = 'authenticated');

-- =====================================================
-- SAMPLE DATA FOR TESTING
-- =====================================================
-- Comprehensive sample data to test all functionality immediately
--
-- SAFE INSERTION APPROACH:
-- Uses "INSERT ... SELECT ... WHERE NOT EXISTS" pattern to prevent
-- duplicate key violations when re-running the schema.
-- This makes the schema safe to run multiple times without errors.
-- =====================================================

-- Insert sample products with diverse categories (with safe duplicate handling)
INSERT INTO products (name, net_weight, price, stock_quantity, category)
SELECT 'Lucky Me Pancit Canton', '60g', 15.00, 50, 'Instant Foods'
WHERE NOT EXISTS (SELECT 1 FROM products WHERE name = 'Lucky Me Pancit Canton');

INSERT INTO products (name, net_weight, price, stock_quantity, category)
SELECT 'Coca-Cola', '330ml', 25.00, 30, 'Beverages'
WHERE NOT EXISTS (SELECT 1 FROM products WHERE name = 'Coca-Cola');

INSERT INTO products (name, net_weight, price, stock_quantity, category)
SELECT 'Corned Beef', '150g', 45.00, 20, 'Canned Goods'
WHERE NOT EXISTS (SELECT 1 FROM products WHERE name = 'Corned Beef');

INSERT INTO products (name, net_weight, price, stock_quantity, category)
SELECT 'Shampoo Sachet', '12ml', 8.00, 100, 'Personal Care'
WHERE NOT EXISTS (SELECT 1 FROM products WHERE name = 'Shampoo Sachet');

INSERT INTO products (name, net_weight, price, stock_quantity, category)
SELECT 'Instant Coffee', '25g', 12.00, 75, 'Beverages'
WHERE NOT EXISTS (SELECT 1 FROM products WHERE name = 'Instant Coffee');

INSERT INTO products (name, net_weight, price, stock_quantity, category)
SELECT 'Rice', '1kg', 55.00, 25, 'Rice & Grains'
WHERE NOT EXISTS (SELECT 1 FROM products WHERE name = 'Rice');

INSERT INTO products (name, net_weight, price, stock_quantity, category)
SELECT 'Soy Sauce', '200ml', 18.00, 40, 'Condiments'
WHERE NOT EXISTS (SELECT 1 FROM products WHERE name = 'Soy Sauce');

INSERT INTO products (name, net_weight, price, stock_quantity, category)
SELECT 'Detergent Powder', '35g', 6.00, 80, 'Household Items'
WHERE NOT EXISTS (SELECT 1 FROM products WHERE name = 'Detergent Powder');

INSERT INTO products (name, net_weight, price, stock_quantity, category)
SELECT 'Bread Loaf', '450g', 35.00, 15, 'Bakery'
WHERE NOT EXISTS (SELECT 1 FROM products WHERE name = 'Bread Loaf');

INSERT INTO products (name, net_weight, price, stock_quantity, category)
SELECT 'Cooking Oil', '1L', 85.00, 12, 'Cooking Essentials'
WHERE NOT EXISTS (SELECT 1 FROM products WHERE name = 'Cooking Oil');

-- Insert sample customer profiles (with safe duplicate handling)
INSERT INTO customers (customer_name, customer_family_name, phone_number, address, notes)
SELECT 'Juan', 'Dela Cruz', '09123456789', 'Barangay San Jose, Quezon City', 'Regular customer - prefers instant foods'
WHERE NOT EXISTS (SELECT 1 FROM customers WHERE customer_name = 'Juan' AND customer_family_name = 'Dela Cruz');

INSERT INTO customers (customer_name, customer_family_name, phone_number, address, notes)
SELECT 'Maria', 'Santos', '09234567890', 'Barangay Maligaya, Manila', 'Frequent buyer of beverages'
WHERE NOT EXISTS (SELECT 1 FROM customers WHERE customer_name = 'Maria' AND customer_family_name = 'Santos');

INSERT INTO customers (customer_name, customer_family_name, phone_number, address, notes)
SELECT 'Pedro', 'Garcia', '09345678901', 'Barangay Bagong Silang, Caloocan', 'Prefers canned goods'
WHERE NOT EXISTS (SELECT 1 FROM customers WHERE customer_name = 'Pedro' AND customer_family_name = 'Garcia');

INSERT INTO customers (customer_name, customer_family_name, phone_number, address, notes)
SELECT 'Ana', 'Reyes', '09456789012', 'Barangay Tatalon, Quezon City', 'Coffee lover - regular customer'
WHERE NOT EXISTS (SELECT 1 FROM customers WHERE customer_name = 'Ana' AND customer_family_name = 'Reyes');

INSERT INTO customers (customer_name, customer_family_name, phone_number, address, notes)
SELECT 'Jose', 'Cruz', '09567890123', 'Barangay Payatas, Quezon City', 'Bulk rice buyer'
WHERE NOT EXISTS (SELECT 1 FROM customers WHERE customer_name = 'Jose' AND customer_family_name = 'Cruz');

INSERT INTO customers (customer_name, customer_family_name, phone_number, address, notes)
SELECT 'Rosa', 'Mendoza', '***********', 'Barangay Commonwealth, Quezon City', 'Family with young children'
WHERE NOT EXISTS (SELECT 1 FROM customers WHERE customer_name = 'Rosa' AND customer_family_name = 'Mendoza');

INSERT INTO customers (customer_name, customer_family_name, phone_number, address, notes)
SELECT 'Carlos', 'Villanueva', '***********', 'Barangay Fairview, Quezon City', 'Small business owner'
WHERE NOT EXISTS (SELECT 1 FROM customers WHERE customer_name = 'Carlos' AND customer_family_name = 'Villanueva');

-- Insert sample debt records (with safe duplicate handling)
INSERT INTO customer_debts (customer_name, customer_family_name, product_name, product_price, quantity, debt_date, notes)
SELECT 'Juan', 'Dela Cruz', 'Lucky Me Pancit Canton', 15.00, 3, '2024-01-15', 'Regular customer'
WHERE NOT EXISTS (
    SELECT 1 FROM customer_debts
    WHERE customer_name = 'Juan' AND customer_family_name = 'Dela Cruz'
    AND product_name = 'Lucky Me Pancit Canton' AND debt_date = '2024-01-15'
);

INSERT INTO customer_debts (customer_name, customer_family_name, product_name, product_price, quantity, debt_date, notes)
SELECT 'Maria', 'Santos', 'Coca-Cola', 25.00, 2, '2024-01-16', 'Neighbor customer'
WHERE NOT EXISTS (
    SELECT 1 FROM customer_debts
    WHERE customer_name = 'Maria' AND customer_family_name = 'Santos'
    AND product_name = 'Coca-Cola' AND debt_date = '2024-01-16'
);

INSERT INTO customer_debts (customer_name, customer_family_name, product_name, product_price, quantity, debt_date, notes)
SELECT 'Pedro', 'Garcia', 'Rice', 55.00, 1, '2024-01-17', 'Weekly rice purchase'
WHERE NOT EXISTS (
    SELECT 1 FROM customer_debts
    WHERE customer_name = 'Pedro' AND customer_family_name = 'Garcia'
    AND product_name = 'Rice' AND debt_date = '2024-01-17'
);

INSERT INTO customer_debts (customer_name, customer_family_name, product_name, product_price, quantity, debt_date, notes)
SELECT 'Ana', 'Reyes', 'Instant Coffee', 12.00, 5, '2024-01-18', 'Coffee lover - bulk purchase'
WHERE NOT EXISTS (
    SELECT 1 FROM customer_debts
    WHERE customer_name = 'Ana' AND customer_family_name = 'Reyes'
    AND product_name = 'Instant Coffee' AND debt_date = '2024-01-18'
);

-- Insert sample payment records (with safe duplicate handling)
INSERT INTO customer_payments (customer_name, customer_family_name, payment_amount, payment_date, responsible_family_member, notes)
SELECT 'Juan', 'Dela Cruz', 30.00, '2024-01-20', 'Ana Dela Cruz', 'Partial payment by daughter'
WHERE NOT EXISTS (
    SELECT 1 FROM customer_payments
    WHERE customer_name = 'Juan' AND customer_family_name = 'Dela Cruz'
    AND payment_date = '2024-01-20' AND payment_amount = 30.00
);

INSERT INTO customer_payments (customer_name, customer_family_name, payment_amount, payment_date, payment_method, notes)
SELECT 'Maria', 'Santos', 50.00, '2024-01-18', 'GCash', 'Full payment for recent purchases'
WHERE NOT EXISTS (
    SELECT 1 FROM customer_payments
    WHERE customer_name = 'Maria' AND customer_family_name = 'Santos'
    AND payment_date = '2024-01-18' AND payment_amount = 50.00
);

INSERT INTO customer_payments (customer_name, customer_family_name, payment_amount, payment_date, payment_method, notes)
SELECT 'Pedro', 'Garcia', 25.00, '2024-01-19', 'Cash', 'Partial payment for rice'
WHERE NOT EXISTS (
    SELECT 1 FROM customer_payments
    WHERE customer_name = 'Pedro' AND customer_family_name = 'Garcia'
    AND payment_date = '2024-01-19' AND payment_amount = 25.00
);

INSERT INTO customer_payments (customer_name, customer_family_name, payment_amount, payment_date, responsible_family_member, notes)
SELECT 'Ana', 'Reyes', 60.00, '2024-01-21', 'Jose Reyes', 'Full payment by husband'
WHERE NOT EXISTS (
    SELECT 1 FROM customer_payments
    WHERE customer_name = 'Ana' AND customer_family_name = 'Reyes'
    AND payment_date = '2024-01-21' AND payment_amount = 60.00
);


-- =====================================================
-- VERIFICATION AND SETUP CONFIRMATION
-- =====================================================
-- Queries to verify successful setup and display completion message

-- Display completion message with features
DO $$
BEGIN
    RAISE NOTICE '🎉 =====================================================';
    RAISE NOTICE '🎉 TINDAHAN STORE DATABASE SETUP COMPLETE!';
    RAISE NOTICE '🎉 =====================================================';
    RAISE NOTICE '';
    RAISE NOTICE '✅ FEATURES SUCCESSFULLY INSTALLED:';
    RAISE NOTICE '   📦 Product Management with inventory tracking';
    RAISE NOTICE '   👥 Customer Profile Management with Cloudinary support';
    RAISE NOTICE '   💰 Debt Management System with payment tracking';
    RAISE NOTICE '   👨‍👩‍👧‍👦 Family member payment responsibility tracking';
    RAISE NOTICE '   📊 Real-time balance calculations';
    RAISE NOTICE '   🔒 Row Level Security (RLS) policies';
    RAISE NOTICE '   ⚡ Performance optimized with comprehensive indexing';
    RAISE NOTICE '   🕒 Automatic timestamp management';
    RAISE NOTICE '   📝 Sample data for immediate testing';
    RAISE NOTICE '';
    RAISE NOTICE '📋 TABLES CREATED:';
    RAISE NOTICE '   • products (inventory management)';
    RAISE NOTICE '   • customers (profile management)';
    RAISE NOTICE '   • customer_debts (debt tracking)';
    RAISE NOTICE '   • customer_payments (payment records)';
    RAISE NOTICE '';
    RAISE NOTICE '👁️ VIEWS CREATED:';
    RAISE NOTICE '   • customer_balances (real-time balance calculations)';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 READY TO USE! You can now:';
    RAISE NOTICE '   • Manage product inventory';
    RAISE NOTICE '   • Add and manage customer profiles';
    RAISE NOTICE '   • Track customer debts and payments';
    RAISE NOTICE '   • Monitor family member payment responsibilities';
    RAISE NOTICE '   • View real-time customer balances';
    RAISE NOTICE '';
    RAISE NOTICE '📊 SAMPLE DATA SUMMARY:';
END $$;

-- Verify tables were created and show summary
SELECT 'TABLES VERIFICATION:' as info;
SELECT
    table_name,
    (SELECT COUNT(*) FROM information_schema.columns
     WHERE table_name = t.table_name AND table_schema = 'public') as column_count
FROM information_schema.tables t
WHERE table_schema = 'public'
AND table_name IN ('products', 'customers', 'customer_debts', 'customer_payments')
ORDER BY table_name;

-- Verify views were created
SELECT 'VIEWS VERIFICATION:' as info;
SELECT table_name as view_name
FROM information_schema.views
WHERE table_schema = 'public'
AND table_name = 'customer_balances';

-- Verify indexes were created
SELECT 'INDEXES VERIFICATION:' as info;
SELECT COUNT(*) as total_indexes_created
FROM pg_indexes
WHERE schemaname = 'public'
AND indexname LIKE 'idx_%';

-- Sample data verification with detailed counts
SELECT 'SAMPLE DATA VERIFICATION:' as info;
SELECT 'Products: ' || COUNT(*) as count FROM products
UNION ALL
SELECT 'Customers: ' || COUNT(*) as count FROM customers
UNION ALL
SELECT 'Customer Debts: ' || COUNT(*) as count FROM customer_debts
UNION ALL
SELECT 'Customer Payments: ' || COUNT(*) as count FROM customer_payments
UNION ALL
SELECT 'Customer Balances: ' || COUNT(*) as count FROM customer_balances;

-- Show sample customer balances to verify the system is working
SELECT 'SAMPLE CUSTOMER BALANCES:' as info;
SELECT
    customer_name || ' ' || customer_family_name as customer,
    'PHP ' || total_debt::text as total_debt,
    'PHP ' || total_payments::text as total_payments,
    'PHP ' || remaining_balance::text as remaining_balance
FROM customer_balances
ORDER BY remaining_balance DESC
LIMIT 5;

-- =====================================================
-- SETUP COMPLETE - READY FOR PRODUCTION USE
-- =====================================================
-- Your Tindahan store database is now fully configured and ready!
--
-- 🎯 WHAT'S INCLUDED:
-- ✅ Complete product inventory management
-- ✅ Customer profile management with Cloudinary support
-- ✅ Comprehensive debt management system
-- ✅ Payment tracking with family member responsibility
-- ✅ Real-time balance calculations
-- ✅ Row Level Security for data protection
-- ✅ Performance optimized with proper indexing
-- ✅ Automatic timestamp management
-- ✅ Sample data for immediate testing
--
-- 🚀 NEXT STEPS:
-- 1. Connect your application to the database
-- 2. Use the API endpoints to interact with the data
-- 3. Start adding real products and customers
-- 4. Begin tracking debts and payments
-- 5. Monitor customer balances in real-time
--
-- 💡 PRO TIPS:
-- • Use the customer_balances view for quick balance lookups
-- • The system automatically calculates totals and remaining balances
-- • All tables have proper indexing for optimal performance
-- • Row Level Security is enabled - configure policies as needed
-- • Sample data can be safely removed when ready for production
-- =====================================================
